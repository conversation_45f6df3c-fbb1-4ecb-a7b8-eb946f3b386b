from django.urls import path
from . import views
from . import views_planejamento
from . import views_movimentacao

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),
    path('dashboard/kpi/', views.dashboard_kpi, name='dashboard-kpi'),
    path('dashboard/executivo/', views.dashboard_executivo, name='dashboard-executivo'),
    path('dashboard/producao/', views_planejamento.dashboard_producao, name='dashboard-producao'),

    # Molas
    path('molas/', views.MolaListView.as_view(), name='mola-list'),
    path('molas/<int:pk>/', views.MolaDetailView.as_view(), name='mola-detail'),
    path('molas/nova/', views.MolaCreateView.as_view(), name='mola-create'),
    path('molas/<int:pk>/editar/', views.MolaUpdateView.as_view(), name='mola-update'),
    path('molas/<int:pk>/excluir/', views.MolaDeleteView.as_view(), name='mola-delete'),

    # Materiais Padrão
    path('materiais-padrao/', views.MaterialPadraoListView.as_view(), name='material-padrao-list'),
    path('materiais-padrao/<int:pk>/', views.MaterialPadraoDetailView.as_view(), name='material-padrao-detail'),
    path('materiais-padrao/novo/', views.MaterialPadraoCreateView.as_view(), name='material-padrao-create'),
    path('materiais-padrao/<int:pk>/editar/', views.MaterialPadraoUpdateView.as_view(), name='material-padrao-update'),
    path('materiais-padrao/<int:pk>/excluir/', views.MaterialPadraoDeleteView.as_view(), name='material-padrao-delete'),
    path('materiais-padrao/pdf/', views.materiais_padrao_pdf, name='materiais-padrao-pdf'),

    # Materiais
    path('materiais/', views.MaterialListView.as_view(), name='material-list'),
    path('materiais/<int:pk>/', views.MaterialDetailView.as_view(), name='material-detail'),
    path('materiais/novo/', views.MaterialCreateView.as_view(), name='material-create'),
    path('materiais/<int:pk>/editar/', views.MaterialUpdateView.as_view(), name='material-update'),
    path('materiais/<int:pk>/excluir/', views.MaterialDeleteView.as_view(), name='material-delete'),
    path('materiais/json/', views.materiais_json, name='materiais-json'),
    path('materiais/pdf/', views.materiais_pdf, name='materiais-pdf'),
    path('api/material/<int:material_id>/', views.material_api, name='material-api'),
    path('api/material/filtrar/', views.filtrar_materiais_api, name='filtrar-materiais-api'),

    # Movimentações de Estoque
    path('movimentacoes/estoque/', views.MovimentacaoEstoqueListView.as_view(), name='movimentacao-estoque-list'),
    path('movimentacoes/estoque/nova/', views.MovimentacaoEstoqueCreateView.as_view(), name='movimentacao-estoque-create'),
    path('movimentacoes/estoque/multipla/', views.movimentacao_multipla, name='movimentacao-multipla'),

    # Movimentações de Material
    path('movimentacoes/material/', views.MovimentacaoMaterialListView.as_view(), name='movimentacao-material-list'),
    path('movimentacoes/material/nova/', views.MovimentacaoMaterialCreateView.as_view(), name='movimentacao-material-create'),

    # Pedidos de Venda
    path('pedidos/', views.PedidoVendaListView.as_view(), name='pedido-venda-list'),
    path('estoque/pedido-venda/', views.PedidoVendaListView.as_view(), name='pedido-venda-list-alt'),
    path('pedido-venda/', views.PedidoVendaListView.as_view(), name='pedido-venda-list-alt2'),
    path('pedidos/<int:pk>/', views.PedidoVendaDetailView.as_view(), name='pedido-venda-detail'),
    path('pedidos/novo/', views.PedidoVendaCreateView.as_view(), name='pedido-venda-create'),
    path('pedidos/<int:pk>/editar/', views.PedidoVendaUpdateView.as_view(), name='pedido-venda-update'),
    path('pedidos/<int:pk>/adicionar-item/', views.pedido_venda_add_item, name='pedido-venda-add-item'),
    path('pedidos/<int:pk>/adicionar-item-multiplo/', views.pedido_venda_add_item_multiplo, name='pedido-venda-add-item-multiplo'),
    path('pedidos/<int:pk>/processar/', views.pedido_venda_processar, name='pedido-venda-processar'),
    path('pedidos/<int:pk>/cancelar/', views.pedido_venda_cancelar, name='pedido-venda-cancelar'),
    path('pedidos/<int:pk>/excluir/', views.PedidoVendaDeleteView.as_view(), name='pedido-venda-delete'),
    path('pedidos/item/<int:pk>/excluir/', views.ItemPedidoDeleteView.as_view(), name='item-pedido-delete'),



    # Relatórios
    path('relatorios/molas-mais-vendidas/', views.relatorio_molas_mais_vendidas, name='relatorio-molas-mais-vendidas'),
    path('relatorios/estoque/', views.relatorio_estoque, name='relatorio-estoque'),
    path('relatorios/vendas-por-mola/', views.relatorio_vendas_por_mola, name='relatorio-vendas-por-mola'),
    path('relatorios/molas-nao-vendidas/', views.relatorio_molas_nao_vendidas, name='relatorio-molas-nao-vendidas'),
    path('auditoria-sistema/', views.auditoria_sistema, name='auditoria-sistema'),
    path('api/vendas-mensais/', views.vendas_mensais_json, name='vendas-mensais-json'),

    # Alertas
    path('alertas/estoque/', views.alertas_estoque, name='alertas-estoque'),

    # Exportação de relatórios
    path('previsao-demanda/<int:pk>/pdf/', views.previsao_demanda_pdf, name='previsao-demanda-pdf'),
    path('previsao-demanda/pdf/', views.previsao_demanda_pdf_list, name='previsao-demanda-pdf-list'),
    path('analise-obsolescencia/pdf/', views.analise_obsolescencia_pdf, name='analise-obsolescencia-pdf'),
    path('analise-obsolescencia/csv/', views.analise_obsolescencia_csv, name='analise-obsolescencia-csv'),

    # Previsão de Demanda
    path('previsao-demanda/', views.PrevisaoDemandaListView.as_view(), name='previsao-demanda-list'),
    path('previsao-demanda/<int:pk>/', views.PrevisaoDemandaDetailView.as_view(), name='previsao-demanda-detail'),
    path('previsao-demanda/gerar/', views.gerar_previsao_demanda, name='gerar-previsao-demanda'),
    path('previsao-demanda/gerar-todas/', views.gerar_todas_previsoes, name='gerar-todas-previsoes'),
    path('previsao-demanda/grupo/<str:grupo_sessao>/', views.previsao_demanda_grupo, name='previsao-demanda-grupo'),
    path('previsao-demanda/grupo/<str:grupo_sessao>/delete/', views.previsao_demanda_grupo_delete, name='previsao-demanda-grupo-delete'),

    # Análise de Obsolescência
    path('analise-obsolescencia/', views.AnaliseObsolescenciaListView.as_view(), name='analise-obsolescencia-list'),
    path('estoque/analise-obsolescencia/', views.AnaliseObsolescenciaListView.as_view(), name='analise-obsolescencia-list-alt'),
    path('analise-obsolescencia/<int:pk>/', views.AnaliseObsolescenciaDetailView.as_view(), name='analise-obsolescencia-detail'),
    path('analise-obsolescencia/gerar/', views.gerar_analise_obsolescencia, name='gerar-analise-obsolescencia'),

    # Ordens de Fabricação
    path('planejamento/', views_planejamento.PlanejamentoProducaoListView.as_view(), name='planejamento-list'),
    path('estoque/planejamento-producao/', views_planejamento.PlanejamentoProducaoListView.as_view(), name='planejamento-list-alt'),
    path('planejamento-producao/', views_planejamento.PlanejamentoProducaoListView.as_view(), name='planejamento-list-alt2'),
    path('planejamento/<int:pk>/', views_planejamento.PlanejamentoProducaoDetailView.as_view(), name='planejamento-detail'),
    path('planejamento/novo/', views_planejamento.PlanejamentoProducaoCreateView.as_view(), name='planejamento-create'),
    path('planejamento/<int:pk>/editar/', views_planejamento.PlanejamentoProducaoUpdateView.as_view(), name='planejamento-update'),
    path('planejamento/<int:pk>/excluir/', views_planejamento.PlanejamentoProducaoDeleteView.as_view(), name='planejamento-delete'),
    path('planejamento/<int:pk>/concluir/', views_planejamento.concluir_planejamento, name='planejamento-concluir'),
    path('planejamento/<int:pk>/cancelar/', views_planejamento.cancelar_planejamento, name='planejamento-cancelar'),
    path('planejamento/<int:pk>/finalizar/', views_planejamento.finalizar_ordem_fabricacao, name='finalizar-ordem-fabricacao'),

    # Itens de Ordem de Fabricação
    path('planejamento/<int:planejamento_id>/item/novo/', views_planejamento.ItemPlanejamentoCreateView.as_view(), name='item-planejamento-create'),
    path('planejamento/item/<int:pk>/editar/', views_planejamento.ItemPlanejamentoUpdateView.as_view(), name='item-planejamento-update'),
    path('planejamento/item/<int:pk>/excluir/', views_planejamento.ItemPlanejamentoDeleteView.as_view(), name='item-planejamento-delete'),
    path('planejamento/item/<int:pk>/iniciar/', views_planejamento.iniciar_producao, name='item-planejamento-iniciar'),
    path('planejamento/item/<int:pk>/registrar/', views_planejamento.registrar_producao, name='item-planejamento-registrar'),

    # Ordens Automáticas
    path('planejamento/automatico/', views_planejamento.planejamento_automatico, name='planejamento-automatico'),

    # API para obter material por mola
    path('planejamento/get_material_by_mola/<int:mola_id>/', views_planejamento.get_material_by_mola, name='get-material-by-mola'),

    # Limpar histórico de movimentações
    path('movimentacoes/estoque/limpar-historico/', views_movimentacao.limpar_historico_estoque, name='limpar-historico-estoque'),
    path('movimentacoes/material/limpar-historico/', views_movimentacao.limpar_historico_material, name='limpar-historico-material'),

    # Backups de movimentações
    path('movimentacoes/estoque/backups/', views_movimentacao.listar_backups_estoque, name='listar-backups-estoque'),
    path('movimentacoes/material/backups/', views_movimentacao.listar_backups_material, name='listar-backups-material'),
    path('movimentacoes/estoque/backups/<int:pk>/restaurar/', views_movimentacao.restaurar_backup_estoque, name='restaurar-backup-estoque'),
    path('movimentacoes/material/backups/<int:pk>/restaurar/', views_movimentacao.restaurar_backup_material, name='restaurar-backup-material'),


]
