{% extends 'estoque/base.html' %}

{% block title %}Materiais - Molas Rios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Materiais</h1>
        <div>
            <a href="{% url 'materiais-pdf' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="btn btn-success" title="Exportar PDF">
                <i class="fas fa-file-pdf"></i> Exportar PDF
            </a>
            <a href="{% url 'material-padrao-list' %}" class="btn btn-info">
                <i class="fas fa-list"></i> Materiais Padrão
            </a>
            <a href="{% url 'material-create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Novo Material
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="id_nome" class="form-label">Nome</label>
                    <input type="text" name="nome" id="id_nome" class="form-control" value="{{ filter.form.nome.value|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="id_diametro" class="form-label">Diâmetro</label>
                    <input type="text" name="diametro" id="id_diametro" class="form-control" value="{{ filter.form.diametro.value|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="id_fornecedor" class="form-label">Fornecedor</label>
                    <input type="text" name="fornecedor" id="id_fornecedor" class="form-control" value="{{ filter.form.fornecedor.value|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="id_codigo_mola" class="form-label">Código da Mola</label>
                    <input type="text" name="codigo_mola" id="id_codigo_mola" class="form-control" value="{{ filter.form.codigo_mola.value|default:'' }}" data-bs-toggle="tooltip" data-bs-placement="top" title="Filtrar materiais compatíveis com a mola">
                </div>
                <div class="col-md-3">
                    <label for="id_estoque_baixo" class="form-label">Estoque Baixo</label>
                    <div class="form-check mt-2">
                        <input type="checkbox" name="estoque_baixo" id="id_estoque_baixo" class="form-check-input" {% if filter.form.estoque_baixo.value %}checked{% endif %}>
                        <label class="form-check-label" for="id_estoque_baixo">Mostrar apenas com estoque baixo</label>
                    </div>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrar
                    </button>
                    <a href="{% url 'material-list' %}" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Limpar Filtros
                    </a>
                </div>
            </form>
        </div>
    </div>

    {% if mola_selecionada %}
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-info text-white">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-info-circle"></i> Mola Selecionada: {{ mola_selecionada.codigo }}
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Cliente:</strong> {{ mola_selecionada.cliente }}
                </div>
                <div class="col-md-3">
                    <strong>Material Padrão:</strong> {{ mola_selecionada.material_padrao|default:"Não definido" }}
                </div>
                <div class="col-md-3">
                    <strong>Peso Unitário:</strong> {{ mola_selecionada.peso_unitario|default:"Não definido" }}g
                </div>
                <div class="col-md-3">
                    <strong>Estoque Atual:</strong> {{ mola_selecionada.quantidade_estoque }} unidades
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="card shadow">
        <div class="card-body">
            {% if materiais %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Diâmetro</th>
                                <th>Fornecedor</th>
                                <th>Estoque (kg)</th>
                                <th>Status</th>
                                {% if mola_selecionada %}
                                <th>Qnt. Potencial</th>
                                {% endif %}
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for material in materiais %}
                                <tr>
                                    <td>{{ material.nome }}</td>
                                    <td>
                                        {% if material.material_padrao %}
                                            {{ material.material_padrao.diametro }}
                                        {% else %}
                                            {{ material.diametro }}
                                        {% endif %}
                                    </td>
                                    <td>{{ material.fornecedor|default:"—" }}</td>
                                    <td>{{ material.quantidade_estoque }}</td>
                                    <td>
                                        {% if material.quantidade_estoque == 0 %}
                                            <span class="badge bg-secondary">N/A</span>
                                        {% elif material.quantidade_estoque <= material.estoque_minimo %}
                                            <span class="badge bg-danger">Estoque Baixo</span>
                                        {% else %}
                                            <span class="badge bg-success">OK</span>
                                        {% endif %}
                                    </td>
                                    {% if mola_selecionada %}
                                    <td>
                                        {% if material.quantidade_potencial %}
                                            {{ material.quantidade_potencial|floatformat:0 }} unidades
                                        {% else %}
                                            —
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    <td>
                                        <a href="{% url 'material-detail' material.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Informações do Material">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a href="{% url 'material-update' material.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Editar Material">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'material-delete' material.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Excluir Material">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        <a href="{% url 'movimentacao-material-create' %}?material={{ material.id }}&tipo=E" class="btn btn-sm btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Adicionar ao Estoque">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <a href="{% url 'movimentacao-material-create' %}?material={{ material.id }}&tipo=S" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Remover do Estoque">
                                            <i class="fas fa-minus"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                {% if is_paginated %}
                    <nav aria-label="Paginação">
                        <ul class="pagination justify-content-center mt-4">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Primeira">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Anterior">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Primeira">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Anterior">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ num }}</a>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Próxima">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Última">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Próxima">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Última">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Nenhum material encontrado.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Inicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    // Melhorar experiência do usuário com filtro por código de mola
    document.addEventListener('DOMContentLoaded', function() {
        const codigoMolaInput = document.getElementById('id_codigo_mola');

        if (codigoMolaInput) {
            // Adicionar evento para destacar visualmente quando código de mola for preenchido
            codigoMolaInput.addEventListener('input', function() {
                if (this.value.trim()) {
                    // Mostrar dica visual de que está filtrando por mola específica
                    this.style.borderColor = '#6f42c1';
                    this.style.boxShadow = '0 0 0 0.2rem rgba(111, 66, 193, 0.25)';
                } else {
                    // Remover estilo especial
                    this.style.borderColor = '';
                    this.style.boxShadow = '';
                }
            });

            // Aplicar estilo inicial se já houver valor
            if (codigoMolaInput.value.trim()) {
                codigoMolaInput.style.borderColor = '#6f42c1';
                codigoMolaInput.style.boxShadow = '0 0 0 0.2rem rgba(111, 66, 193, 0.25)';
            }
        }
    });
</script>
{% endblock %}
